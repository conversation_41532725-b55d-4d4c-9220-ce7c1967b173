class_name Player
extends CharacterBody3D

## Main player controller implementing movement, combat, and interaction systems
## Uses CharacterBody3D for Godot 4.4 physics integration

@export var move_speed: float = 5.0
@export var acceleration: float = 10.0
@export var friction: float = 10.0
@export var rotation_speed: float = 10.0

# Node references
@onready var stats: PlayerStats = $PlayerStats
@onready var input_handler: PlayerInput = $PlayerInput
@onready var animation_controller: PlayerAnimations = $PlayerAnimations
@onready var combat_system: Node = $CombatSystem
@onready var inventory: Node = $Inventory

# Movement state
var input_direction: Vector2 = Vector2.ZERO
var last_movement_direction: Vector3 = Vector3.FORWARD
var is_moving: bool = false

# Combat state
var auto_attack_target: Node3D = null
var auto_attack_timer: float = 0.0
var is_attacking: bool = false

# Flight system (wings)
var is_flying: bool = false
var flight_time_remaining: float = 0.0
var flight_cooldown_timer: float = 0.0

# Status effects
var active_status_effects: Dictionary = {}

func _ready() -> void:
	_setup_player()
	_connect_signals()

func _setup_player() -> void:
	# Set up collision layers
	collision_layer = 1  # Player layer
	collision_mask = 2 | 4  # Enemies and Environment
	
	# Initialize components
	if not stats:
		stats = PlayerStats.new()
		add_child(stats)
	
	if not input_handler:
		input_handler = PlayerInput.new()
		add_child(input_handler)

func _connect_signals() -> void:
	EventBus.player_died.connect(_on_player_died)
	EventBus.auto_attack_triggered.connect(_on_auto_attack_triggered)
	
	if input_handler:
		input_handler.movement_input.connect(_on_movement_input)
		input_handler.skill_activated.connect(_on_skill_activated)

func _physics_process(delta: float) -> void:
	_update_movement(delta)
	_update_combat(delta)
	_update_flight(delta)
	_update_status_effects(delta)

## Movement System
func _update_movement(delta: float) -> void:
	var target_velocity = Vector3.ZERO
	
	if input_direction.length() > 0:
		# Convert 2D input to 3D movement
		target_velocity = Vector3(input_direction.x, 0, input_direction.y)
		target_velocity = target_velocity.normalized() * get_current_move_speed()
		
		# Update last movement direction for facing
		last_movement_direction = target_velocity.normalized()
		is_moving = true
	else:
		is_moving = false
	
	# Apply movement with acceleration/friction
	if target_velocity.length() > 0:
		velocity = velocity.move_toward(target_velocity, acceleration * delta)
	else:
		velocity = velocity.move_toward(Vector3.ZERO, friction * delta)
	
	# Handle gravity (unless flying)
	if not is_flying and not is_on_floor():
		velocity.y += get_gravity().y * delta
	
	# Apply movement
	move_and_slide()
	
	# Update rotation to face movement direction
	if is_moving and not is_attacking:
		_update_rotation(delta)
	
	# Emit position updates
	EventBus.player_position_changed.emit(global_position)

func get_current_move_speed() -> float:
	var base_speed = move_speed
	if stats:
		base_speed = stats.get_move_speed()
	
	# Apply flight speed bonus
	if is_flying:
		base_speed *= GameConstants.WING_SPEED_MULTIPLIER
	
	# Apply status effect modifiers
	if active_status_effects.has(GameEnums.StatusEffect.HASTE):
		base_speed *= 1.5
	elif active_status_effects.has(GameEnums.StatusEffect.SLOW):
		base_speed *= 0.5
	
	return base_speed

func _update_rotation(delta: float) -> void:
	if last_movement_direction.length() > 0:
		var target_rotation = atan2(last_movement_direction.x, last_movement_direction.z)
		rotation.y = lerp_angle(rotation.y, target_rotation, rotation_speed * delta)

## Combat System
func _update_combat(delta: float) -> void:
	if auto_attack_timer > 0:
		auto_attack_timer -= delta
	
	# Auto-attack logic
	if auto_attack_timer <= 0 and auto_attack_target:
		_perform_auto_attack()

func _perform_auto_attack() -> void:
	if not auto_attack_target or not is_instance_valid(auto_attack_target):
		auto_attack_target = null
		return
	
	# Check range
	var distance = global_position.distance_to(auto_attack_target.global_position)
	var attack_range = GameConstants.AUTO_ATTACK_RANGE
	
	if distance > attack_range:
		auto_attack_target = null
		return
	
	# Perform attack
	is_attacking = true
	var damage = _calculate_attack_damage()
	
	# Face target
	var direction_to_target = (auto_attack_target.global_position - global_position).normalized()
	rotation.y = atan2(direction_to_target.x, direction_to_target.z)
	
	# Deal damage
	if auto_attack_target.has_method("take_damage"):
		auto_attack_target.take_damage(damage)
	
	# Set attack cooldown
	var attack_speed = stats.get_attack_speed() if stats else 1.0
	auto_attack_timer = 1.0 / attack_speed
	
	# Trigger effects
	EventBus.damage_dealt.emit(auto_attack_target, damage, "physical")
	EventBus.auto_attack_triggered.emit(auto_attack_target)
	
	# Animation
	if animation_controller:
		animation_controller.play_attack_animation()
	
	# Reset attack state after animation
	get_tree().create_timer(0.5).timeout.connect(func(): is_attacking = false)

func _calculate_attack_damage() -> int:
	var base_damage = stats.get_attack() if stats else 10
	
	# Add weapon damage if equipped
	if inventory and inventory.has_method("get_equipped_weapon"):
		var weapon = inventory.get_equipped_weapon()
		if weapon:
			var weapon_damage = weapon.calculate_damage(base_damage)
			return weapon_damage.final_damage
	
	return base_damage

func find_nearest_enemy() -> Node3D:
	var enemies = get_tree().get_nodes_in_group("enemies")
	var nearest_enemy = null
	var nearest_distance = GameConstants.AUTO_ATTACK_RANGE
	
	for enemy in enemies:
		if enemy is Node3D and is_instance_valid(enemy):
			var distance = global_position.distance_to(enemy.global_position)
			if distance < nearest_distance:
				nearest_enemy = enemy
				nearest_distance = distance
	
	return nearest_enemy

## Flight System
func _update_flight(delta: float) -> void:
	if flight_cooldown_timer > 0:
		flight_cooldown_timer -= delta
	
	if is_flying:
		flight_time_remaining -= delta
		if flight_time_remaining <= 0:
			_end_flight()

func start_flight() -> bool:
	if flight_cooldown_timer > 0 or is_flying:
		return false
	
	is_flying = true
	flight_time_remaining = GameConstants.FLIGHT_DURATION_BASE
	
	# Visual effects
	EventBus.effect_requested.emit("flight_start", global_position)
	
	return true

func _end_flight() -> void:
	is_flying = false
	flight_cooldown_timer = GameConstants.FLIGHT_COOLDOWN
	
	# Visual effects
	EventBus.effect_requested.emit("flight_end", global_position)

## Status Effects
func apply_status_effect(effect: GameEnums.StatusEffect, duration: float, intensity: float = 1.0) -> void:
	active_status_effects[effect] = {
		"duration": duration,
		"intensity": intensity,
		"start_time": Time.get_time_dict_from_system()["unix"]
	}
	
	_apply_status_effect_immediate(effect, intensity)

func _update_status_effects(delta: float) -> void:
	var effects_to_remove = []
	
	for effect in active_status_effects:
		var effect_data = active_status_effects[effect]
		effect_data.duration -= delta
		
		if effect_data.duration <= 0:
			effects_to_remove.append(effect)
		else:
			_update_status_effect_tick(effect, effect_data, delta)
	
	for effect in effects_to_remove:
		remove_status_effect(effect)

func _apply_status_effect_immediate(effect: GameEnums.StatusEffect, intensity: float) -> void:
	match effect:
		GameEnums.StatusEffect.STUN:
			input_direction = Vector2.ZERO
		GameEnums.StatusEffect.SHIELD:
			# Apply damage reduction
			pass
		GameEnums.StatusEffect.BERSERK:
			# Increase attack speed
			var modifier = {GameEnums.StatType.ATTACK_SPEED: 0.5 * intensity}
			stats.add_temporary_modifier(modifier, active_status_effects[effect].duration)

func _update_status_effect_tick(effect: GameEnums.StatusEffect, effect_data: Dictionary, delta: float) -> void:
	match effect:
		GameEnums.StatusEffect.POISON:
			var damage = int(5 * effect_data.intensity)
			stats.take_damage(damage)
		GameEnums.StatusEffect.BURN:
			var damage = int(8 * effect_data.intensity)
			stats.take_damage(damage)
		GameEnums.StatusEffect.REGENERATION:
			var heal = int(3 * effect_data.intensity)
			stats.heal(heal)

func remove_status_effect(effect: GameEnums.StatusEffect) -> void:
	if active_status_effects.has(effect):
		_remove_status_effect_immediate(effect)
		active_status_effects.erase(effect)

func _remove_status_effect_immediate(effect: GameEnums.StatusEffect) -> void:
	match effect:
		GameEnums.StatusEffect.STUN:
			# Restore movement
			pass
		GameEnums.StatusEffect.SHIELD:
			# Remove damage reduction
			pass

## Event Handlers
func _on_movement_input(direction: Vector2) -> void:
	input_direction = direction

func _on_skill_activated(skill_id: String) -> void:
	match skill_id:
		"dash":
			_perform_dash()
		"flight":
			start_flight()
		_:
			# Handle other skills
			pass

func _on_auto_attack_triggered(target: Node) -> void:
	if target != auto_attack_target:
		auto_attack_target = target

func _on_player_died() -> void:
	# Handle player death
	is_attacking = false
	auto_attack_target = null
	input_direction = Vector2.ZERO
	
	# Play death animation
	if animation_controller:
		animation_controller.play_death_animation()

## Skills
func _perform_dash() -> void:
	if last_movement_direction.length() > 0:
		var dash_distance = 5.0
		var dash_target = global_position + last_movement_direction * dash_distance
		
		# Create tween for smooth dash movement
		var tween = create_tween()
		tween.tween_property(self, "global_position", dash_target, 0.2)
		
		# Visual effects
		EventBus.effect_requested.emit("dash", global_position)

## Utility Functions
func get_health_percentage() -> float:
	return stats.get_health_percentage() if stats else 1.0

func get_mana_percentage() -> float:
	return stats.get_mana_percentage() if stats else 1.0

func is_alive() -> bool:
	return stats.current_health > 0 if stats else true

func get_level() -> int:
	return stats.current_level if stats else 1

## Debug Functions
func debug_print_state() -> void:
	print("=== Player Debug Info ===")
	print("Position: %s" % global_position)
	print("Velocity: %s" % velocity)
	print("Is Moving: %s" % is_moving)
	print("Is Attacking: %s" % is_attacking)
	print("Is Flying: %s" % is_flying)
	print("Auto Attack Target: %s" % auto_attack_target)
	print("Active Status Effects: %s" % active_status_effects.keys())
	if stats:
		print("Health: %d/%d" % [stats.current_health, stats.get_max_health()])
		print("Level: %d" % stats.current_level)
	print("========================")
