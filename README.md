# Project MUHero - 3D Roguelite Action RPG

A mobile-optimized 3D roguelite action RPG built with Godot 4.4, featuring fast-paced combat, persistent progression, and stylized visuals.

## 🎮 Game Overview

**Genre**: 3D Roguelite Action RPG  
**Platform**: Mobile (Android/iOS) with desktop support  
**Engine**: Godot 4.4  
**Perspective**: Hybrid Isometric/Top-down  
**Art Style**: Stylized semi-realistic (Genshin Impact + League of Legends aesthetic)

### Core Features
- **Fast-paced Combat**: Auto-attack system with active skills and combos
- **Persistent Progression**: Character levels, gear, and skill trees that persist between runs
- **Run-based Gameplay**: Procedural wave-based levels with increasing difficulty
- **Wings System**: Unique flight mechanics for mobility and combat
- **Mobile-Optimized**: Touch controls, virtual joystick, and performance optimization

## 🏗️ Architecture Overview

The project follows **SOLID principles** and implements several design patterns for maintainability and scalability:

### Design Patterns Used
- **Singleton Pattern**: Core managers (GameManager, SaveSystem, etc.)
- **Observer Pattern**: EventBus for decoupled communication
- **Factory Pattern**: ItemFactory for dynamic item creation
- **Strategy Pattern**: Different item behaviors and damage calculations
- **Template Method Pattern**: Base item class with specialized implementations

### Core Systems

#### 📁 Project Structure
```
├── core/                   # Core game systems
│   ├── EventBus.gd        # Global event communication
│   ├── GameManager.gd     # Main game state controller
│   ├── SaveSystem.gd      # Persistent data management
│   └── ResourceManager.gd # Asset loading and caching
├── data/                   # Game data and constants
│   ├── GameEnums.gd       # All game enumerations
│   └── GameConstants.gd   # Configuration values
├── player/                 # Player systems
│   ├── Player.gd          # Main player controller
│   ├── PlayerStats.gd     # Stat management with modifiers
│   └── PlayerInput.gd     # Mobile input handling
├── items/                  # Item system
│   ├── Item.gd            # Base item class
│   ├── ItemFactory.gd     # Factory for item creation
│   ├── Weapon.gd          # Weapon specialization
│   ├── Armor.gd           # Armor specialization
│   └── Wings.gd           # Wings specialization
├── combat/                 # Combat mechanics
│   └── DamageCalculator.gd # Damage calculation system
├── ui/                     # User interface
│   ├── HUD.gd             # In-game HUD controller
│   └── HUD.tscn           # HUD scene
└── scenes/                 # Game scenes
    ├── Main.gd            # Main scene controller
    └── Main.tscn          # Main game scene
```

## 🎯 Key Systems Implementation

### 1. Event-Driven Architecture
The `EventBus` singleton provides decoupled communication between systems:

```gdscript
# Example: Player takes damage
EventBus.damage_dealt.emit(target, damage, "physical")
EventBus.player_health_changed.emit(current_health, max_health)
```

### 2. Modular Item System
Items use inheritance and composition for flexibility:

```gdscript
# Factory creates items with proper stats and rarity
var sword = ItemFactory.create_item("sword_basic", GameEnums.ItemRarity.EPIC, 10)
sword.equip()  # Automatically applies stat bonuses
```

### 3. Mobile-Optimized Input
Supports both touch and keyboard input with virtual joystick:

```gdscript
# Touch-anywhere joystick for movement
# Skill buttons with haptic feedback
# Gesture recognition for special actions
```

### 4. Performance Optimization
- Object pooling for frequently created objects
- Resource caching and async loading
- Mobile-specific rendering optimizations
- Efficient stat calculation with caching

## 🔧 Technical Features

### Godot 4.4 Specific Implementations
- **Forward+ Rendering**: Mobile-friendly stylized visuals
- **CharacterBody3D**: Modern physics-based player controller
- **Resource System**: Type-safe data management
- **Signals**: Event-driven programming
- **Autoloads**: Singleton pattern implementation

### Mobile Optimizations
- **Touch Controls**: Virtual joystick and gesture recognition
- **UI Scaling**: Adaptive UI for different screen sizes
- **Performance**: Object pooling and LOD systems
- **Battery**: Optimized rendering and background processing

### Clean Code Practices
- **Meaningful Names**: Clear, descriptive variable and function names
- **Small Functions**: Single responsibility principle
- **No Magic Numbers**: All values in GameConstants
- **Type Safety**: Explicit typing throughout
- **Documentation**: Comprehensive code comments

## 🎮 Gameplay Systems

### Combat System
- **Auto-Attack**: Automatic targeting and attacking
- **Active Skills**: Player-triggered abilities with cooldowns
- **Damage Types**: Physical, Magical, True, Critical, Excellent
- **Status Effects**: Buffs, debuffs, and damage over time

### Progression System
- **Character Levels**: Persistent progression with stat bonuses
- **Skill Trees**: Passive abilities and active skill upgrades
- **Gear System**: Equipment with stats, sockets, and set bonuses
- **Wings System**: Unique flight mechanics and customization

### Item System
- **Rarity Tiers**: Common to Divine with increasing power
- **Socket System**: Runes for customization
- **Set Bonuses**: Equipment synergies
- **Durability**: Equipment maintenance mechanics

## 🚀 Getting Started

### Prerequisites
- Godot 4.4 or later
- Basic understanding of GDScript
- Mobile development setup (for deployment)

### Running the Project
1. Clone the repository
2. Open in Godot 4.4
3. Run the Main scene (`scenes/Main.tscn`)
4. Use WASD for movement, 1-2 for skills, or touch controls on mobile

### Development Setup
1. Ensure all autoloads are properly configured in project settings
2. Check input map for proper key bindings
3. Verify physics layers are set correctly
4. Test on target mobile devices for performance

## 📱 Mobile Deployment

### Android
- Configure export template
- Set up proper permissions
- Test on various screen sizes
- Optimize for different performance levels

### iOS
- Set up Apple Developer account
- Configure provisioning profiles
- Test on actual devices
- Submit for App Store review

## 🔮 Future Enhancements

### Planned Features
- **Multiplayer Hub**: Social features and player interaction
- **More Biomes**: Additional environments and enemies
- **Crafting System**: Item creation and enhancement
- **Guild System**: Social progression and cooperation
- **PvP Arena**: Competitive gameplay modes

### Technical Improvements
- **Cloud Save**: Cross-device progression
- **Analytics**: Player behavior tracking
- **A/B Testing**: Feature experimentation
- **Localization**: Multi-language support

## 🤝 Contributing

This project follows clean code principles and SOLID design patterns. When contributing:

1. Follow the established architecture patterns
2. Use meaningful names and proper documentation
3. Implement proper error handling
4. Write unit tests for new systems
5. Ensure mobile compatibility

## 📄 License

This project is for educational and portfolio purposes. Please respect the design patterns and architectural decisions when studying or extending the codebase.

---

**Built with ❤️ using Godot 4.4 and clean architecture principles**
