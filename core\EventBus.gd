class_name EventBus
extends Node

## Global event bus for decoupled communication between systems
## Follows the Observer pattern for clean architecture

# Player Events
signal player_health_changed(current_health: int, max_health: int)
signal player_died()
signal player_level_up(new_level: int)
signal player_stats_changed(stats: Dictionary)
signal player_position_changed(position: Vector3)

# Combat Events
signal damage_dealt(target: Node, damage: int, damage_type: String)
signal enemy_killed(enemy: Node, experience: int)
signal critical_hit(target: Node, damage: int)
signal skill_used(skill_name: String, cooldown: float)
signal auto_attack_triggered(target: Node)

# Item Events
signal item_equipped(item: Item, slot: String)
signal item_unequipped(item: Item, slot: String)
signal item_picked_up(item: Item)
signal item_dropped(item: Item)
signal inventory_changed()
signal gear_stats_updated(total_stats: Dictionary)

# Skill Events
signal skill_unlocked(skill_id: String)
signal passive_skill_activated(skill_id: String)
signal skill_tree_updated()
signal skill_point_spent(skill_id: String, points_remaining: int)

# UI Events
signal ui_panel_opened(panel_name: String)
signal ui_panel_closed(panel_name: String)
signal notification_shown(message: String, type: String)
signal hud_update_requested()

# Game State Events
signal game_started()
signal game_paused()
signal game_resumed()
signal run_started(biome: String)
signal run_completed(success: bool, rewards: Array)
signal wave_started(wave_number: int)
signal wave_completed(wave_number: int)

# Progression Events
signal experience_gained(amount: int)
signal currency_changed(currency_type: String, amount: int)
signal achievement_unlocked(achievement_id: String)

# Loot Events
signal loot_dropped(loot_data: Dictionary, position: Vector3)
signal rare_loot_dropped(item: Item, position: Vector3)

# Audio Events
signal sound_requested(sound_name: String, position: Vector3 = Vector3.ZERO)
signal music_changed(track_name: String)

# VFX Events
signal effect_requested(effect_name: String, position: Vector3, target: Node = null)
signal screen_shake_requested(intensity: float, duration: float)

# Mobile Events
signal touch_input_detected(position: Vector2, type: String)
signal virtual_joystick_moved(direction: Vector2)

# Multiplayer Events (for future hub implementation)
signal player_joined_hub(player_data: Dictionary)
signal player_left_hub(player_id: String)
signal chat_message_received(player_name: String, message: String)

func _ready() -> void:
	print("EventBus initialized - Global communication system ready")

## Emit a generic event with data
func emit_event(event_name: String, data: Dictionary = {}) -> void:
	print("EventBus: Emitting event '%s' with data: %s" % [event_name, data])
	# This allows for dynamic event emission if needed
	
## Log all signal connections for debugging
func debug_connections() -> void:
	print("EventBus: Active signal connections:")
	for signal_name in get_signal_list():
		var connections = get_signal_connection_list(signal_name.name)
		if connections.size() > 0:
			print("  %s: %d connections" % [signal_name.name, connections.size()])
